#ifndef SAVINGSACCOUNT_H
#define SAVINGSACCOUNT_H
#include "account.h"
#include "accumulator.h"
class SavingsAccount : public Account {// 储蓄账户类
private:
    Accumulator acc;  // 辅助计算利息的累加器
    double rate;      // 存款的年利率
public:
    SavingsAccount(const Date& date, const std::string& id, double rate);
    void deposit(const Date& date, double amount, const std::string& desc);
    void withdraw(const Date& date, double amount, const std::string& desc);
    void settle(const Date& date);
    void show() const;
};
#endif
