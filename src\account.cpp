#include "account.h"
#include <iostream>
#include <cmath>
using namespace std;

double Account::total = 0;
multimap<Date, AccountRecord> Account::recordMap;
Account::Account(const Date& date, const string& id) : id(id), balance(0) {
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay()
         << "\t#" << id << " created" << endl;
}
void Account::record(const Date& date, double amount, const string& desc) {//这是记账用的函数
    total -= balance; // 先从总金额中减去当前账户余额
    amount = floor(amount * 100 + 0.5) / 100; // 对显示的amount进行四舍五入
    balance += amount;   // 更新余额
    total += balance; // 将新余额加到总金额中

    // 创建账目记录并添加到recordMap中
    AccountRecord record(date, id, amount, balance, desc);
    recordMap.insert(make_pair(date, record));

    // 显示账目记录
    cout << date.getYear() << "-" << date.getMonth() << "-" << date.getDay()
         << "\t#" << id << "\t" << amount << "\t" << balance;
    if (!desc.empty()) {
        cout << "\t" << desc;
    }
    cout << endl;
}
string Account::getId() const {
    return id;
}
double Account::getBalance() const {
    return balance;
}
void Account::show() const {
    cout << id << "\tBalance: " << balance;
}
double Account::getTotal() {
    return total;
}

void Account::query(const Date& start, const Date& end) {
    auto iter_start = recordMap.lower_bound(start);  // 使用multimap的lower_bound和upper_bound方法查找指定日期范围内的记录
    auto iter_end = recordMap.upper_bound(end);
    for (auto iter = iter_start; iter != iter_end; ++iter) { // 显示查询结果
        iter->second.show();
    }
}
