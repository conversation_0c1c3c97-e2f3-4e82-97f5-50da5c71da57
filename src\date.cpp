#include "date.h"
#include <iostream>
#include <limits>
#include <cstdio>
using namespace std;
const int DAYS_PER_MONTH[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};// 存储平年每月天数
bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}
Date::Date(int year, int month, int day) : year(year), month(month), day(day) {
}
int Date::totalDays() const {// 计算该日期是从公元元年1月1日开始的第几天
    int result = 0;
    for (int i = 1; i < year; i++) {
        result += isLeapYear(i) ? 366 : 365;
    } // 计算年份的天数
 for (int i = 1; i < month; i++) {
        result += DAYS_PER_MONTH[i];
        if (i == 2 && isLeapYear(year)) {
            result += 1;
        }
    }// 计算当年已过月份的天数
result += day;// 加上当月天数
    return result;
}
int Date::operator-(const Date& date) const {// 计算两个日期相差天数
    return totalDays() - date.totalDays();
}
void Date::show() const {
    cout << year << "-" << month << "-" << day;
}

int Date::getMaxDay() const {
    int maxDay = DAYS_PER_MONTH[month];
    if (month == 2 && isLeapYear(year)) {
        maxDay++; // 2月添加一天
    }
    return maxDay;
}

bool Date::operator<(const Date& date) const {
    if (year != date.year)
        return year < date.year;
    if (month != date.month)
        return month < date.month;
    return day < date.day;
}

Date Date::read() {
    int year, month, day;
    char buffer[20];
    cin >> buffer;
    if (sscanf(buffer, "%d/%d/%d", &year, &month, &day) == 3) {
        return Date(year, month, day);// 成功解析日期
    } else {
        return Date(1900, 1, 1); // 解析失败，使用默认日期
    }
}
