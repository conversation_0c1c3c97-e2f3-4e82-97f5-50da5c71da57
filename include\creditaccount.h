#ifndef CREDITACCOUNT_H
#define CREDITACCOUNT_H
#include "account.h"
#include "accumulator.h"
// 这是信用卡账户类
class CreditAccount : public Account {
private:
    Accumulator acc;      // 辅助计算利息的累加器
    double credit;        // 信用额度
    double rate;          // 欠款的日利率
    double fee;           // 信用卡年费
    Date lastFeeDate;     // 上次收取年费的日期
public:
    CreditAccount(const Date& date, const std::string& id, double credit, double rate, double fee);
    double getCredit() const;
    double getAvailableCredit() const;// 获取可用信用额度
    double getRate() const; // 获取日利率
   double getFee() const;
void deposit(const Date& date, double amount, const std::string& desc);
    void withdraw(const Date& date, double amount, const std::string& desc);
    void settle(const Date& date);
    void show() const;
};
#endif

