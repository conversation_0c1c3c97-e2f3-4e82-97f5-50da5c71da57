#include "accountrecord.h"
#include <iostream>
using namespace std;
AccountRecord::AccountRecord(const Date& date, const string& account, 
                           double amount, double balance, const string& desc)
    : date(date), account(account), amount(amount), balance(balance), desc(desc) {
}
void AccountRecord::show() const {
    date.show();
    cout << "\t#" << account << "\t" << amount << "\t" << balance;
    if (!desc.empty()) {
        cout << "\t" << desc;
    }
    cout << endl;
}
